# Use official LiteLLM image as base (without database dependencies)
FROM ghcr.io/berriai/litellm:main-latest

# Set working directory
WORKDIR /app

# Copy configuration file
COPY litellm_config.yaml /app/config.yaml

# Expose port (Railway will set PORT env var dynamically)
EXPOSE $PORT

# Set environment variables
ENV LITELLM_LOG=INFO

# Start LiteLLM with config
# Railway will provide PORT via environment variable
CMD ["sh", "-c", "litellm --config /app/config.yaml --port ${PORT:-4000} --num_workers 1"]
