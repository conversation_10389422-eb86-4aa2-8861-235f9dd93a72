model_list:
  # DeepSeek Models (Primary - Cost Effective)
  - model_name: deepseek-chat
    litellm_params:
      model: deepseek-chat
      api_key: os.environ/DEEPSEEK_API_KEY
      
  - model_name: deepseek-coder
    litellm_params:
      model: deepseek-coder
      api_key: os.environ/DEEPSEEK_API_KEY
      
  - model_name: deepseek-reasoner
    litellm_params:
      model: deepseek-reasoner
      api_key: os.environ/DEEPSEEK_API_KEY

  # Qwen Models via OpenRouter
  - model_name: qwen/qwen-2.5-72b-instruct
    litellm_params:
      model: openrouter/qwen/qwen-2.5-72b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: https://openrouter.ai/api/v1
      
  - model_name: qwen/qwen-2.5-32b-instruct
    litellm_params:
      model: openrouter/qwen/qwen-2.5-32b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: https://openrouter.ai/api/v1
      
  - model_name: qwen/qwen-2.5-coder-32b-instruct
    litellm_params:
      model: openrouter/qwen/qwen-2.5-coder-32b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: https://openrouter.ai/api/v1

  # OpenAI Models (Fallback)
  - model_name: gpt-4o
    litellm_params:
      model: gpt-4o
      api_key: os.environ/OPENAI_API_KEY
      
  - model_name: gpt-4o-mini
    litellm_params:
      model: gpt-4o-mini
      api_key: os.environ/OPENAI_API_KEY

  # Anthropic Models (Fallback)
  - model_name: claude-3-5-sonnet-********
    litellm_params:
      model: claude-3-5-sonnet-********
      api_key: os.environ/ANTHROPIC_API_KEY
      
  - model_name: claude-3-5-haiku-********
    litellm_params:
      model: claude-3-5-haiku-********
      api_key: os.environ/ANTHROPIC_API_KEY

# General settings
general_settings:
  master_key: os.environ/LITELLM_MASTER_KEY
  database_url: "sqlite:///litellm.db"
  
# Logging
litellm_settings:
  set_verbose: true
  json_logs: true
  log_level: "INFO"

# Rate limiting and routing
router_settings:
  routing_strategy: "least-busy"
  model_group_alias: 
    "reasoning": ["deepseek-reasoner", "qwen/qwen-2.5-72b-instruct"]
    "coding": ["deepseek-coder", "qwen/qwen-2.5-coder-32b-instruct"]
    "general": ["deepseek-chat", "qwen/qwen-2.5-32b-instruct"]
