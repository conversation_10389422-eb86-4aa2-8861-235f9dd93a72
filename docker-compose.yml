version: '3.8'

services:
  litellm:
    build: .
    ports:
      - "4000:4000"
    env_file:
      - .env
    volumes:
      - ./litellm_config.yaml:/app/config.yaml
      - litellm_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  litellm_data:
